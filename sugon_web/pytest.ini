[pytest]
# 最低pytest版本要求
minversion = 7.4.0

# 测试路径
testpaths = sugon_web/testcase

# 控制台输出中文乱码问题
disable_test_id_escaping_and_forfeit_all_rights_to_community_support = True


# 默认命令行选项
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --alluredir=allure-result
    --clean-alluredir

# 自定义标记
markers =
    smoke: 冒烟测试
    regression: 回归测试
    slow: 慢速测试
    login: 登录相关测试
    evs: 云硬盘相关测试
    ecs: 弹性云服务器相关测试
    
# 测试文件和函数命名模式
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)s] %(filename)s:%(lineno)d %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# 忽略的目录
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    allure-result
    allure-report
    screenshots
    logs

# 过滤警告
;filterwarnings =
;    ignore::DeprecationWarning
;    ignore::PendingDeprecationWarning
;    ignore:.*playwright.*:UserWarning

# 并行执行配置（如果使用pytest-xdist）
# addopts = -n auto

# JUnit XML报告（如果需要CI集成）
# junit_family = xunit2