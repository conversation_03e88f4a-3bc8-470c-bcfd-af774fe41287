INFO     sugon_web.utils.logger:conftest.py:86 === SETUP START: test_volume_create[True-30-页面测试描述] ===
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.address`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.address` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.automotive`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.automotive` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.bank`.
DEBUG    faker.factory:factory.py:88 Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.barcode`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.barcode` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.color`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.color` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.company`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.company` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.credit_card`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.credit_card` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.currency`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.currency` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.date_time`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.date_time` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:109 Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.geo`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.geo` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.internet`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.internet` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:109 Provider `faker.providers.isbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.job`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.job` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.lorem`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.lorem` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.misc`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.misc` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.person`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.person` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.phone_number`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.phone_number` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:109 Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:109 Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.ssn`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.ssn` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:109 Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
INFO     sugon_web.utils.logger:conftest.py:30 测试环境配置加载完成
INFO     sugon_web.utils.logger:conftest.py:40 开始初始化浏览器: type=chromium, headless=True
DEBUG    asyncio:selector_events.py:54 Using selector: KqueueSelector
INFO     sugon_web.utils.logger:conftest.py:51 启动 chromium 浏览器...
INFO     sugon_web.utils.logger:conftest.py:57 chromium 浏览器启动成功
INFO     sugon_web.utils.logger:conftest.py:59 创建浏览器上下文...
INFO     sugon_web.utils.logger:conftest.py:61 浏览器上下文创建成功
INFO     sugon_web.utils.logger:conftest.py:63 创建新页面...
INFO     sugon_web.utils.logger:conftest.py:65 页面创建成功
INFO     sugon_web.utils.logger:conftest.py:67 导航到目标URL: https://172.22.1.170:30000
INFO     sugon_web.utils.logger:conftest.py:69 页面导航完成，当前URL: https://172.22.1.170:30000/
INFO     sugon_web.utils.logger:playwright.py:49 通过占位符定位元素: 请输入登录账号
INFO     sugon_web.utils.logger:playwright.py:49 通过占位符定位元素: 请输入登录账号
INFO     sugon_web.utils.logger:playwright.py:49 通过占位符定位元素: 请输入登录密码
INFO     sugon_web.utils.logger:playwright.py:59 通过文本内容定位元素: 登 录
INFO     sugon_web.utils.logger:playwright.py:44 通过角色和名称定位元素: role=button, name=Close
INFO     sugon_web.utils.logger:playwright.py:103 成功悬停元素: 资源中心
INFO     sugon_web.utils.logger:playwright.py:103 成功悬停元素: 存储
INFO     sugon_web.utils.logger:playwright.py:78 成功点击元素: 云硬盘
INFO     sugon_web.utils.logger:base.py:239 成功导航到服务: 资源中心 -> 存储 -> 云硬盘
INFO     sugon_web.utils.logger:conftest.py:98 === TEARDOWN START: test_volume_create[True-30-页面测试描述] ===