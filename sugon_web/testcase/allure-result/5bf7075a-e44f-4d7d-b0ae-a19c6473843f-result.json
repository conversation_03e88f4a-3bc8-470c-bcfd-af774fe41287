{"name": "test_volume_create[False-50-test_description]", "status": "broken", "statusDetails": {"message": "AttributeError: 'str' object has no attribute 'iter_parents'", "trace": "../../.venv/lib/python3.11/site-packages/allure_pytest/listener.py:95: in pytest_runtest_setup\n    self._update_fixtures_children(item)\n../../.venv/lib/python3.11/site-packages/allure_pytest/listener.py:64: in _update_fixtures_children\n    for fixturedef in _test_fixtures(item):\n../../.venv/lib/python3.11/site-packages/allure_pytest/listener.py:302: in _test_fixtures\n    fixturedefs_pytest = fixturemanager.getfixturedefs(name, item.nodeid)\nE   AttributeError: 'str' object has no attribute 'iter_parents'"}, "attachments": [{"name": "log", "source": "5891def4-d279-4fd2-ab71-243f3ed21353-attachment.txt", "type": "text/plain"}], "start": 1755443602588, "stop": 1755443602588, "uuid": "f0af832c-4882-46c7-b57c-cf09a63b9d29", "labels": [{"name": "feature", "value": "云硬盘 EVS"}, {"name": "epic", "value": "存储服务"}, {"name": "parentSuite", "value": "testcase"}, {"name": "suite", "value": "test_evs"}, {"name": "subSuite", "value": "TestEVS"}, {"name": "host", "value": "MacBook-Air"}, {"name": "thread", "value": "76155-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.test_evs"}]}