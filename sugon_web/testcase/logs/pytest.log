2025-08-17 23:13:17 [INFO] conftest.py:86 === SETUP START: test_volume_create[True-30-页面测试描述] ===
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.address`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.address` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.automotive`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.automotive` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.bank`.
2025-08-17 23:13:17 [DEBUG] factory.py:88 Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.barcode`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.barcode` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.color`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.color` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.company`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.company` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.credit_card`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.credit_card` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.currency`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.currency` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.date_time`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.date_time` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:109 Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.geo`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.geo` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.internet`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.internet` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:109 Provider `faker.providers.isbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.job`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.job` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.lorem`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.lorem` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.misc`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.misc` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.person`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.person` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.phone_number`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.phone_number` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:109 Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-17 23:13:17 [DEBUG] factory.py:109 Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-17 23:13:17 [DEBUG] factory.py:78 Looking for locale `en_US` in provider `faker.providers.ssn`.
2025-08-17 23:13:17 [DEBUG] factory.py:97 Provider `faker.providers.ssn` has been localized to `en_US`.
2025-08-17 23:13:17 [DEBUG] factory.py:109 Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
2025-08-17 23:13:17 [INFO] conftest.py:30 测试环境配置加载完成
2025-08-17 23:13:17 [INFO] conftest.py:40 开始初始化浏览器: type=chromium, headless=True
2025-08-17 23:13:17 [DEBUG] selector_events.py:54 Using selector: KqueueSelector
2025-08-17 23:13:18 [INFO] conftest.py:51 启动 chromium 浏览器...
2025-08-17 23:13:18 [INFO] conftest.py:57 chromium 浏览器启动成功
2025-08-17 23:13:18 [INFO] conftest.py:59 创建浏览器上下文...
2025-08-17 23:13:18 [INFO] conftest.py:61 浏览器上下文创建成功
2025-08-17 23:13:18 [INFO] conftest.py:63 创建新页面...
2025-08-17 23:13:18 [INFO] conftest.py:65 页面创建成功
2025-08-17 23:13:18 [INFO] conftest.py:67 导航到目标URL: https://172.22.1.170:30000
2025-08-17 23:13:20 [INFO] conftest.py:69 页面导航完成，当前URL: https://172.22.1.170:30000/
2025-08-17 23:13:20 [INFO] playwright.py:49 通过占位符定位元素: 请输入登录账号
2025-08-17 23:13:21 [INFO] playwright.py:49 通过占位符定位元素: 请输入登录账号
2025-08-17 23:13:21 [INFO] playwright.py:49 通过占位符定位元素: 请输入登录密码
2025-08-17 23:13:21 [INFO] playwright.py:59 通过文本内容定位元素: 登 录
2025-08-17 23:13:21 [INFO] playwright.py:44 通过角色和名称定位元素: role=button, name=Close
2025-08-17 23:13:22 [INFO] playwright.py:103 成功悬停元素: 资源中心
2025-08-17 23:13:22 [INFO] playwright.py:103 成功悬停元素: 存储
2025-08-17 23:13:22 [INFO] playwright.py:78 成功点击元素: 云硬盘
2025-08-17 23:13:22 [INFO] base.py:239 成功导航到服务: 资源中心 -> 存储 -> 云硬盘
2025-08-17 23:13:22 [ERROR] conftest.py:158 测试设置失败: test_volume_create[True-30-页面测试描述]
2025-08-17 23:13:22 [INFO] conftest.py:98 === TEARDOWN START: test_volume_create[True-30-页面测试描述] ===
2025-08-17 23:13:22 [INFO] conftest.py:86 === SETUP START: test_volume_create[False-50-test_description] ===
2025-08-17 23:13:22 [ERROR] conftest.py:158 测试设置失败: test_volume_create[False-50-test_description]
2025-08-17 23:13:22 [INFO] conftest.py:98 === TEARDOWN START: test_volume_create[False-50-test_description] ===
2025-08-17 23:13:22 [INFO] conftest.py:73 开始清理浏览器资源...
2025-08-17 23:13:22 [INFO] conftest.py:75 浏览器上下文已关闭
2025-08-17 23:13:22 [INFO] conftest.py:77 浏览器已关闭
